<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkbox最终测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .auto-learning-form-group {
            margin-bottom: 16px;
        }
        
        .auto-learning-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .auto-learning-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .auto-learning-checkbox {
            display: flex !important;
            align-items: center !important;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            user-select: none;
        }

        .auto-learning-checkbox:hover {
            background: #e9ecef;
            border-color: #409eff;
        }

        .auto-learning-checkbox input[type="checkbox"] {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            margin: 0 12px 0 0 !important;
            padding: 0 !important;
            width: 18px !important;
            height: 18px !important;
            min-width: 18px !important;
            min-height: 18px !important;
            max-width: 18px !important;
            max-height: 18px !important;
            cursor: pointer !important;
            accent-color: #409eff;
            -webkit-appearance: checkbox !important;
            -moz-appearance: checkbox !important;
            appearance: checkbox !important;
            position: relative !important;
            flex-shrink: 0 !important;
            border: 1px solid #ccc !important;
            border-radius: 2px !important;
            background: white !important;
            outline: none !important;
            box-shadow: none !important;
            transform: none !important;
            z-index: 1 !important;
        }

        .auto-learning-checkbox-label {
            font-weight: 500;
            color: #495057;
            flex: 1;
            cursor: pointer;
        }

        /* 备用自定义checkbox样式 */
        .auto-learning-checkbox.custom-checkbox input[type="checkbox"] {
            display: none !important;
        }

        .auto-learning-checkbox.custom-checkbox::before {
            content: '';
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 12px;
            border: 2px solid #ccc;
            border-radius: 3px;
            background: white;
            flex-shrink: 0;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .auto-learning-checkbox.custom-checkbox.checked::before {
            background: #409eff;
            border-color: #409eff;
        }

        .auto-learning-checkbox.custom-checkbox.checked::after {
            content: '✓';
            position: absolute;
            left: 4px;
            top: -2px;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }
        
        .auto-learning-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .test-controls {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .result {
            background: #f0f9ff;
            border: 1px solid #e0f2fe;
            border-radius: 4px;
            padding: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Checkbox最终测试</h1>
        
        <div class="test-controls">
            <strong>测试控制：</strong><br>
            <button class="auto-learning-button" onclick="testNormalCheckbox()">测试原生Checkbox</button>
            <button class="auto-learning-button" onclick="testCustomCheckbox()">测试自定义Checkbox</button>
            <button class="auto-learning-button" onclick="showSettings()">显示设置</button>
        </div>
        
        <div class="auto-learning-form-group">
            <label class="auto-learning-label">百度OCR API Key</label>
            <input type="text" class="auto-learning-input" id="baidu-ak" value="test-api-key" placeholder="请输入百度API Key">
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox" id="checkbox1">
                <input type="checkbox" id="auto-mute" checked>
                <span class="auto-learning-checkbox-label">🔇 自动静音视频</span>
            </label>
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox" id="checkbox2">
                <input type="checkbox" id="auto-switch-courseware" checked>
                <span class="auto-learning-checkbox-label">🔄 自动切换课件</span>
            </label>
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox" id="checkbox3">
                <input type="checkbox" id="auto-switch-course">
                <span class="auto-learning-checkbox-label">📖 自动切换课程</span>
            </label>
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox" id="checkbox4">
                <input type="checkbox" id="auto-select-course" checked>
                <span class="auto-learning-checkbox-label">📚 自动添加选修课程</span>
            </label>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>测试结果：</h3>
            <div id="result-content"></div>
        </div>
    </div>
    
    <script>
        function testNormalCheckbox() {
            const checkboxes = document.querySelectorAll('.auto-learning-checkbox');
            let result = '<h4>原生Checkbox测试结果：</h4><ul>';
            
            checkboxes.forEach((container, index) => {
                const checkbox = container.querySelector('input[type="checkbox"]');
                const computedStyle = window.getComputedStyle(checkbox);
                const isVisible = computedStyle.display !== 'none' && 
                                computedStyle.visibility !== 'hidden' && 
                                computedStyle.opacity !== '0' &&
                                checkbox.offsetWidth > 0 && 
                                checkbox.offsetHeight > 0;
                
                result += `<li>
                    <strong>Checkbox ${index + 1}:</strong> ${isVisible ? '✅ 可见' : '❌ 不可见'}<br>
                    - Display: ${computedStyle.display}<br>
                    - Visibility: ${computedStyle.visibility}<br>
                    - Opacity: ${computedStyle.opacity}<br>
                    - Size: ${checkbox.offsetWidth}x${checkbox.offsetHeight}px
                </li>`;
            });
            
            result += '</ul>';
            showResult(result);
        }
        
        function testCustomCheckbox() {
            const checkboxes = document.querySelectorAll('.auto-learning-checkbox');
            
            checkboxes.forEach(container => {
                const checkbox = container.querySelector('input[type="checkbox"]');
                enableCustomCheckbox(container, checkbox);
            });
            
            showResult('<h4>自定义Checkbox已启用</h4><p>所有checkbox现在使用自定义样式显示</p>');
        }
        
        function enableCustomCheckbox(container, checkbox) {
            container.classList.add('custom-checkbox');
            if (checkbox.checked) {
                container.classList.add('checked');
            }

            container.addEventListener('click', (e) => {
                e.preventDefault();
                checkbox.checked = !checkbox.checked;
                
                if (checkbox.checked) {
                    container.classList.add('checked');
                } else {
                    container.classList.remove('checked');
                }

                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
            });

            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    container.classList.add('checked');
                } else {
                    container.classList.remove('checked');
                }
            });
        }
        
        function showSettings() {
            const settings = {
                baiduAK: document.getElementById('baidu-ak').value,
                autoMute: document.getElementById('auto-mute').checked,
                autoSwitchCourseware: document.getElementById('auto-switch-courseware').checked,
                autoSwitchCourse: document.getElementById('auto-switch-course').checked,
                autoSelectCourse: document.getElementById('auto-select-course').checked
            };
            
            let html = '<h4>当前设置值：</h4><ul>';
            for (const [key, value] of Object.entries(settings)) {
                if (typeof value === 'boolean') {
                    html += `<li><strong>${key}:</strong> ${value ? '✅ 启用' : '❌ 禁用'}</li>`;
                } else {
                    html += `<li><strong>${key}:</strong> ${value}</li>`;
                }
            }
            html += '</ul>';
            
            showResult(html);
        }
        
        function showResult(content) {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');
            
            resultContent.innerHTML = content;
            resultDiv.style.display = 'block';
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            setTimeout(testNormalCheckbox, 500);
        });
    </script>
</body>
</html>
