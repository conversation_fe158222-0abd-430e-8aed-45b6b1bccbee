<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkbox控件测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .checkbox:hover {
            background: #e9ecef;
            border-color: #409eff;
        }

        .checkbox input[type="checkbox"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #409eff;
        }

        .checkbox-label {
            font-weight: 500;
            color: #495057;
        }
        
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Checkbox控件测试</h1>
        
        <div class="form-group">
            <label class="label">API设置</label>
            <input type="text" class="input" placeholder="请输入API Key">
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-mute" checked>
                <span class="checkbox-label">🔇 自动静音视频</span>
            </label>
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-switch-courseware" checked>
                <span class="checkbox-label">🔄 自动切换课件</span>
            </label>
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-switch-course" checked>
                <span class="checkbox-label">📖 自动切换课程</span>
            </label>
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-select-course" checked>
                <span class="checkbox-label">📚 自动添加选修课程</span>
            </label>
        </div>

        <div class="form-group">
            <button class="button" onclick="showSettings()">显示当前设置</button>
        </div>

        <div id="result" style="display: none; margin-top: 20px; padding: 12px; background: #f0f9ff; border: 1px solid #e0f2fe; border-radius: 4px;">
            <h3>当前设置：</h3>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        function showSettings() {
            const settings = {
                autoMute: document.getElementById('auto-mute').checked,
                autoSwitchCourseware: document.getElementById('auto-switch-courseware').checked,
                autoSwitchCourse: document.getElementById('auto-switch-course').checked,
                autoSelectCourse: document.getElementById('auto-select-course').checked
            };

            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');

            let html = '<ul>';
            for (const [key, value] of Object.entries(settings)) {
                html += `<li><strong>${key}:</strong> ${value ? '✅ 启用' : '❌ 禁用'}</li>`;
            }
            html += '</ul>';

            resultContent.innerHTML = html;
            resultDiv.style.display = 'block';
        }
    </script>
    </div>
</body>
</html>
