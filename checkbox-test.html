<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkbox控件测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .input:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            outline: none;
        }
        
        .checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .checkbox:hover {
            background: #e9ecef;
            border-color: #409eff;
        }

        .checkbox input[type="checkbox"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
            cursor: pointer;
            accent-color: #409eff;
        }

        .checkbox-label {
            font-weight: 500;
            color: #495057;
        }
        
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #66b1ff;
        }
        
        .button.danger {
            background: #f56c6c;
        }
        
        .button.danger:hover {
            background: #f78989;
        }
        
        .result {
            margin-top: 20px;
            padding: 12px;
            border-radius: 4px;
            background: #f0f9ff;
            border: 1px solid #e0f2fe;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Checkbox控件测试</h1>
        
        <div class="form-group">
            <label class="label">百度OCR API Key</label>
            <input type="text" class="input" id="baidu-ak" placeholder="请输入百度API Key">
        </div>

        <div class="form-group">
            <label class="label">百度OCR Secret Key</label>
            <input type="password" class="input" id="baidu-sk" placeholder="请输入百度Secret Key">
        </div>

        <div class="form-group">
            <label class="label">操作延时时间（秒）</label>
            <input type="number" class="input" id="delay-time" value="1" min="0.5" max="10" step="0.5">
        </div>

        <div class="form-group">
            <label class="label">自动选修课程数量</label>
            <input type="number" class="input" id="course-count" value="10" min="1" max="50">
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-mute" checked>
                <span class="checkbox-label">🔇 自动静音视频</span>
            </label>
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-select-course" checked>
                <span class="checkbox-label">📚 自动添加选修课程</span>
            </label>
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-switch-courseware">
                <span class="checkbox-label">🔄 自动切换课件</span>
            </label>
        </div>

        <div class="form-group">
            <label class="checkbox">
                <input type="checkbox" id="auto-switch-course">
                <span class="checkbox-label">📖 自动切换课程</span>
            </label>
        </div>

        <div class="form-group">
            <button class="button" id="save-settings">保存设置</button>
            <button class="button danger" id="reset-settings">重置设置</button>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>当前设置：</h3>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        function updateResult() {
            const settings = {
                baiduAK: document.getElementById('baidu-ak').value,
                baiduSK: document.getElementById('baidu-sk').value,
                delayTime: document.getElementById('delay-time').value,
                courseCount: document.getElementById('course-count').value,
                autoMute: document.getElementById('auto-mute').checked,
                autoSelectCourse: document.getElementById('auto-select-course').checked,
                autoSwitchCourseware: document.getElementById('auto-switch-courseware').checked,
                autoSwitchCourse: document.getElementById('auto-switch-course').checked
            };
            
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');
            
            let html = '<ul>';
            for (const [key, value] of Object.entries(settings)) {
                html += `<li><strong>${key}:</strong> ${value}</li>`;
            }
            html += '</ul>';
            
            resultContent.innerHTML = html;
            resultDiv.style.display = 'block';
        }
        
        document.getElementById('save-settings').addEventListener('click', updateResult);
        
        document.getElementById('reset-settings').addEventListener('click', () => {
            document.getElementById('baidu-ak').value = '';
            document.getElementById('baidu-sk').value = '';
            document.getElementById('delay-time').value = '1';
            document.getElementById('course-count').value = '10';
            document.getElementById('auto-mute').checked = true;
            document.getElementById('auto-select-course').checked = true;
            document.getElementById('auto-switch-courseware').checked = false;
            document.getElementById('auto-switch-course').checked = false;
            
            document.getElementById('result').style.display = 'none';
        });
        
        // 实时更新显示
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('change', () => {
                console.log('设置已更改');
            });
        });
    </script>
</body>
</html>
