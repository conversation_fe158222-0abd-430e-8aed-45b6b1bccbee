<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号管理测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            min-height: 120px;
            line-height: 1.5;
            font-family: monospace;
            resize: vertical;
        }
        
        .input:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            outline: none;
        }
        
        .input::placeholder {
            color: #999;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #66b1ff;
        }
        
        .button.secondary {
            background: #909399;
        }
        
        .button.secondary:hover {
            background: #a6a9ad;
        }
        
        .row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 8px;
        }
        
        .result {
            margin-top: 20px;
            padding: 12px;
            border-radius: 4px;
            background: #f0f9ff;
            border: 1px solid #e0f2fe;
        }
        
        .account-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .account-item:hover {
            background: #e9ecef;
            border-color: #409eff;
        }

        .account-phone {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .account-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            white-space: nowrap;
            min-width: 60px;
            text-align: center;
            background: #909399;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>账号管理功能测试</h1>
        
        <div class="form-group">
            <label class="label">批量添加账号（一行一组：手机号码 密码）</label>
            <textarea 
                class="input" 
                id="account-input" 
                placeholder="每行一组账号密码，例如：&#10;*********** password123&#10;*********** password456&#10;*********** password789" 
                rows="6"
            ></textarea>
            <div class="row">
                <button class="button" id="add-account">批量添加</button>
                <button class="button secondary" id="clear-input">清空输入</button>
            </div>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>处理结果：</h3>
            <div id="result-content"></div>
        </div>
        
        <div id="accounts-list">
            <h3>已添加的账号：</h3>
            <div id="accounts-container"></div>
        </div>
    </div>

    <script>
        // 模拟账号存储
        let accounts = [];
        
        // 自动保存功能
        const accountInput = document.getElementById('account-input');
        
        // 加载保存的内容
        function loadSavedInput() {
            const saved = localStorage.getItem('account_input_draft');
            if (saved) {
                accountInput.value = saved;
                console.log('已恢复保存的输入内容');
            }
        }
        
        // 保存输入内容
        function saveInput() {
            localStorage.setItem('account_input_draft', accountInput.value);
        }
        
        // 清空输入
        function clearInput() {
            accountInput.value = '';
            saveInput();
            console.log('已清空输入框');
        }
        
        // 批量添加账号
        function addAccounts() {
            const inputValue = accountInput.value.trim();
            
            if (!inputValue) {
                alert('请输入账号信息');
                return;
            }
            
            const lines = inputValue.split('\n').map(line => line.trim()).filter(line => line);
            
            if (lines.length === 0) {
                alert('请输入有效的账号信息');
                return;
            }
            
            let addedCount = 0;
            let errorCount = 0;
            const errors = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const lineNumber = i + 1;
                
                const parts = line.split(/\s+/);
                if (parts.length !== 2) {
                    errors.push(`第${lineNumber}行格式错误：${line}`);
                    errorCount++;
                    continue;
                }
                
                const [phone, password] = parts;
                
                if (!/^1[3-9]\d{9}$/.test(phone)) {
                    errors.push(`第${lineNumber}行手机号格式不正确：${phone}`);
                    errorCount++;
                    continue;
                }
                
                if (accounts.some(acc => acc.phone === phone)) {
                    errors.push(`第${lineNumber}行手机号已存在：${phone}`);
                    errorCount++;
                    continue;
                }
                
                accounts.push({
                    phone,
                    password,
                    status: 'pending',
                    addTime: Date.now()
                });
                
                addedCount++;
            }
            
            // 显示结果
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');
            
            let resultHtml = `<p>成功添加：${addedCount} 个账号</p>`;
            if (errorCount > 0) {
                resultHtml += `<p>失败：${errorCount} 个账号</p>`;
                resultHtml += `<div style="margin-top: 10px; font-size: 12px; color: #666;">`;
                resultHtml += errors.slice(0, 5).map(err => `<div>• ${err}</div>`).join('');
                if (errors.length > 5) {
                    resultHtml += `<div>• ...还有${errors.length - 5}个错误</div>`;
                }
                resultHtml += `</div>`;
            }
            
            resultContent.innerHTML = resultHtml;
            resultDiv.style.display = 'block';
            
            if (addedCount > 0) {
                accountInput.value = '';
                saveInput();
                updateAccountsList();
            }
        }
        
        // 更新账号列表显示
        function updateAccountsList() {
            const container = document.getElementById('accounts-container');
            
            if (accounts.length === 0) {
                container.innerHTML = '<p style="color: #666;">暂无账号</p>';
                return;
            }
            
            container.innerHTML = accounts.map((account, index) => `
                <div class="account-item">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div class="account-phone" style="flex: 1; margin-right: 12px;">${account.phone}</div>
                        <span class="account-status" style="margin-right: 12px;">${account.status}</span>
                        <button class="button secondary" onclick="removeAccount(${index})" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                    </div>
                </div>
            `).join('');
        }
        
        // 删除账号
        function removeAccount(index) {
            accounts.splice(index, 1);
            updateAccountsList();
        }
        
        // 事件绑定
        document.getElementById('add-account').addEventListener('click', addAccounts);
        document.getElementById('clear-input').addEventListener('click', clearInput);
        
        // 自动保存
        accountInput.addEventListener('input', saveInput);
        accountInput.addEventListener('paste', () => {
            setTimeout(saveInput, 100);
        });
        
        // 初始化
        loadSavedInput();
        updateAccountsList();
    </script>
</body>
</html>
