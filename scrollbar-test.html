<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动条美化测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .auto-learning-panel {
            position: relative;
            width: 420px;
            max-height: 600px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            font-size: 14px;
            overflow: hidden;
            margin: 20px auto;
        }
        
        .auto-learning-content {
            padding: 16px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .auto-learning-form-group {
            margin-bottom: 16px;
        }
        
        .auto-learning-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .auto-learning-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .auto-learning-input:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            outline: none;
        }
        
        .auto-learning-input[id="account-input"] {
            min-height: 120px;
            line-height: 1.5;
            font-size: 13px;
            resize: vertical;
            font-family: monospace;
        }
        
        .auto-learning-log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 12px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .auto-learning-log-item {
            margin-bottom: 4px;
        }
        
        .auto-learning-log-time {
            color: #666;
            margin-right: 8px;
        }
        
        .auto-learning-log-level-info { color: #409eff; }
        .auto-learning-log-level-success { color: #67c23a; }
        .auto-learning-log-level-warning { color: #e6a23c; }
        .auto-learning-log-level-error { color: #f56c6c; }
        
        /* 滚动条美化 */
        .auto-learning-panel ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .auto-learning-panel ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb:active {
            background: #909090;
        }

        .auto-learning-panel ::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        /* 日志区域滚动条 */
        .auto-learning-log::-webkit-scrollbar {
            width: 6px;
        }

        .auto-learning-log::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        .auto-learning-log::-webkit-scrollbar-thumb {
            background: #dee2e6;
            border-radius: 3px;
        }

        .auto-learning-log::-webkit-scrollbar-thumb:hover {
            background: #adb5bd;
        }

        /* 账号输入框滚动条 */
        .auto-learning-input[id="account-input"]::-webkit-scrollbar {
            width: 6px;
        }

        .auto-learning-input[id="account-input"]::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        .auto-learning-input[id="account-input"]::-webkit-scrollbar-thumb {
            background: #409eff;
            border-radius: 3px;
        }

        .auto-learning-input[id="account-input"]::-webkit-scrollbar-thumb:hover {
            background: #66b1ff;
        }

        /* Firefox滚动条样式 */
        .auto-learning-panel {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }

        .auto-learning-log {
            scrollbar-width: thin;
            scrollbar-color: #dee2e6 #f8f9fa;
        }

        .auto-learning-input[id="account-input"] {
            scrollbar-width: thin;
            scrollbar-color: #409eff #f8f9fa;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .demo-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>滚动条美化效果展示</h1>
        
        <div class="demo-section">
            <div class="demo-title">1. 主面板滚动条</div>
            <div class="demo-description">主面板内容区域的滚动条，宽度8px，圆角设计，悬停效果</div>
            
            <div class="auto-learning-panel">
                <div class="auto-learning-content">
                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">示例内容区域</label>
                        <p>这是一个很长的内容区域，用来展示主面板的滚动条效果。</p>
                        <p>当内容超出容器高度时，会出现美化的滚动条。</p>
                        <p>滚动条采用了圆角设计，颜色为浅灰色。</p>
                        <p>鼠标悬停时颜色会变深，提供良好的交互反馈。</p>
                        <p>这里添加更多内容来触发滚动条显示...</p>
                        <p>第6行内容</p>
                        <p>第7行内容</p>
                        <p>第8行内容</p>
                        <p>第9行内容</p>
                        <p>第10行内容</p>
                        <p>第11行内容</p>
                        <p>第12行内容</p>
                        <p>第13行内容</p>
                        <p>第14行内容</p>
                        <p>第15行内容</p>
                        <p>第16行内容</p>
                        <p>第17行内容</p>
                        <p>第18行内容</p>
                        <p>第19行内容</p>
                        <p>第20行内容</p>
                        <p>第21行内容</p>
                        <p>第22行内容</p>
                        <p>第23行内容</p>
                        <p>第24行内容</p>
                        <p>第25行内容</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">2. 账号输入框滚动条</div>
            <div class="demo-description">多行账号输入框的滚动条，蓝色主题，宽度6px</div>
            
            <div class="auto-learning-form-group">
                <label class="auto-learning-label">批量添加账号</label>
                <textarea class="auto-learning-input" id="account-input" rows="6" placeholder="每行一组账号密码，例如：&#10;*********** password123&#10;*********** password456">*********** password123
*********** password456
*********** password789
*********** password000
*********** password111
*********** password222
*********** password333
*********** password444
*********** password555
*********** password666</textarea>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">3. 日志区域滚动条</div>
            <div class="demo-description">日志显示区域的滚动条，灰色主题，宽度6px</div>
            
            <div class="auto-learning-log">
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:25</span>
                    <span class="auto-learning-log-level-info">[INFO]</span>
                    <span>自动学习助手已初始化</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:26</span>
                    <span class="auto-learning-log-level-success">[SUCCESS]</span>
                    <span>已添加账号: ***********</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:27</span>
                    <span class="auto-learning-log-level-warning">[WARNING]</span>
                    <span>验证码识别失败，正在重试</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:28</span>
                    <span class="auto-learning-log-level-error">[ERROR]</span>
                    <span>登录失败: 密码错误</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:29</span>
                    <span class="auto-learning-log-level-info">[INFO]</span>
                    <span>开始处理下一个账号</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:30</span>
                    <span class="auto-learning-log-level-success">[SUCCESS]</span>
                    <span>登录成功</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:31</span>
                    <span class="auto-learning-log-level-info">[INFO]</span>
                    <span>检测到页面类型: courseMine</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:32</span>
                    <span class="auto-learning-log-level-success">[SUCCESS]</span>
                    <span>找到未完成课程</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:33</span>
                    <span class="auto-learning-log-level-info">[INFO]</span>
                    <span>开始学习课程</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:34</span>
                    <span class="auto-learning-log-level-success">[SUCCESS]</span>
                    <span>视频已静音</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:35</span>
                    <span class="auto-learning-log-level-info">[INFO]</span>
                    <span>视频播放中...</span>
                </div>
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">14:30:36</span>
                    <span class="auto-learning-log-level-success">[SUCCESS]</span>
                    <span>课件学习完成</span>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 13px; color: #666;">
            <h4 style="margin-top: 0; color: #333;">滚动条美化特点：</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li><strong>主面板</strong>：8px宽度，浅灰色，圆角设计</li>
                <li><strong>账号输入框</strong>：6px宽度，蓝色主题，与界面主色调一致</li>
                <li><strong>日志区域</strong>：6px宽度，灰色主题，低调不抢眼</li>
                <li><strong>交互效果</strong>：悬停时颜色变深，提供视觉反馈</li>
                <li><strong>兼容性</strong>：支持Webkit内核和Firefox浏览器</li>
            </ul>
        </div>
    </div>
</body>
</html>
