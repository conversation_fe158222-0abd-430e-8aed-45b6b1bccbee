<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号管理界面预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            min-height: 120px;
            line-height: 1.5;
            font-family: monospace;
            resize: vertical;
        }
        
        .input:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            outline: none;
        }
        
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #66b1ff;
        }
        
        .button.secondary {
            background: #909399;
        }
        
        .button.secondary:hover {
            background: #a6a9ad;
        }
        
        .row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 8px;
        }
        
        .account-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }
        
        .account-item:hover {
            background: #e9ecef;
            border-color: #409eff;
        }
        
        .account-phone {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }
        
        .account-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            white-space: nowrap;
            min-width: 60px;
            text-align: center;
        }
        
        .status-pending { background: #909399; }
        .status-running { background: #409eff; }
        .status-completed { background: #67c23a; }
        .status-error { background: #f56c6c; }
        
        .preview-note {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 20px;
            color: #0050b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="preview-note">
            <strong>界面预览</strong> - 这是修改后的账号管理界面效果预览
        </div>
        
        <h1>账号管理功能</h1>
        
        <div class="form-group">
            <label class="label">批量添加账号（一行一组：手机号码 密码）</label>
            <textarea 
                class="input" 
                placeholder="每行一组账号密码，例如：&#10;*********** password123&#10;*********** password456&#10;*********** password789" 
                rows="6"
            >*********** password123
*********** password456
*********** password789</textarea>
            <div class="row">
                <button class="button">批量添加</button>
                <button class="button secondary">清空输入</button>
            </div>
        </div>
        
        <div>
            <h3>已添加的账号：</h3>
            
            <!-- 示例账号列表 - 同一行显示 -->
            <div class="account-item">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="account-phone" style="flex: 1; margin-right: 12px;">***********</div>
                    <span class="account-status status-pending" style="margin-right: 12px;">等待中</span>
                    <button class="button secondary" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                </div>
            </div>
            
            <div class="account-item">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="account-phone" style="flex: 1; margin-right: 12px;">***********</div>
                    <span class="account-status status-running" style="margin-right: 12px;">学习中</span>
                    <button class="button secondary" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                </div>
            </div>
            
            <div class="account-item">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="account-phone" style="flex: 1; margin-right: 12px;">***********</div>
                    <span class="account-status status-completed" style="margin-right: 12px;">已完成</span>
                    <button class="button secondary" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                </div>
            </div>
            
            <div class="account-item">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="account-phone" style="flex: 1; margin-right: 12px;">***********</div>
                    <span class="account-status status-error" style="margin-right: 12px;">失败</span>
                    <button class="button secondary" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                </div>
            </div>
        </div>
        
        <div class="form-group" style="margin-top: 20px;">
            <button class="button">开始学习</button>
            <button class="button secondary">停止学习</button>
            <button class="button secondary">清空账号</button>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 13px; color: #666;">
            <h4 style="margin-top: 0; color: #333;">主要改进：</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li><strong>多行输入</strong>：支持批量添加多个账号，每行一组</li>
                <li><strong>自动保存</strong>：输入内容自动保存，重新打开时恢复</li>
                <li><strong>同一行显示</strong>：手机号码、学习状态、删除按钮在同一行显示</li>
                <li><strong>状态标签</strong>：美化的状态标签，不同状态不同颜色</li>
                <li><strong>批量处理</strong>：支持批量添加和详细的错误提示</li>
            </ul>
        </div>
    </div>
</body>
</html>
