<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双滚动条修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
        }
        
        .auto-learning-panel {
            position: relative;
            width: 420px;
            max-height: 600px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            overflow: hidden;
        }
        
        .auto-learning-header {
            background: #409eff;
            color: white;
            padding: 12px 16px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .auto-learning-content {
            padding: 16px;
            max-height: 500px;
            overflow-y: auto;
            position: relative;
            padding-bottom: 80px; /* 为底部按钮留出空间 */
        }
        
        .auto-learning-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 16px;
        }
        
        .auto-learning-tab {
            padding: 8px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        
        .auto-learning-tab.active {
            color: #409eff;
            border-bottom-color: #409eff;
        }
        
        .auto-learning-form-group {
            margin-bottom: 16px;
        }
        
        .auto-learning-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .auto-learning-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .auto-learning-input:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            outline: none;
        }
        
        .auto-learning-checkbox {
            display: flex !important;
            align-items: center !important;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            user-select: none;
        }

        .auto-learning-checkbox:hover {
            background: #e9ecef;
            border-color: #409eff;
        }

        .auto-learning-checkbox input[type="checkbox"] {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            margin: 0 12px 0 0 !important;
            padding: 0 !important;
            width: 18px !important;
            height: 18px !important;
            cursor: pointer !important;
            accent-color: #409eff;
        }

        .auto-learning-checkbox-label {
            font-weight: 500;
            color: #495057;
            flex: 1;
            cursor: pointer;
        }
        
        .auto-learning-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.3s ease;
        }
        
        .auto-learning-button:hover {
            background: #66b1ff;
        }
        
        .auto-learning-button.danger {
            background: #f56c6c;
        }
        
        .auto-learning-button.danger:hover {
            background: #f78989;
        }
        
        /* 设置页面容器样式 - 修复后：不设置overflow */
        .auto-learning-settings-container {
            padding-right: 4px;
        }
        
        /* 滚动条美化 - 只有主面板有滚动条 */
        .auto-learning-panel ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .auto-learning-panel ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb:active {
            background: #909090;
        }

        .auto-learning-panel ::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        /* Firefox滚动条样式 */
        .auto-learning-panel {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .demo-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .status-indicator {
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .status-fixed {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-problem {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 修复前的问题演示 -->
        <div class="demo-section">
            <div class="demo-title">修复前：双滚动条问题</div>
            <div class="demo-description">外层和内层都有overflow-y: auto，导致出现双滚动条</div>
            
            <div class="status-indicator status-problem">
                ❌ 问题：嵌套滚动容器导致双滚动条
            </div>
            
            <div class="auto-learning-panel">
                <div class="auto-learning-header">
                    <span>设置配置（问题版本）</span>
                </div>
                <div class="auto-learning-content">
                    <div class="auto-learning-tabs">
                        <div class="auto-learning-tab active">设置配置</div>
                    </div>
                    
                    <!-- 这里模拟有问题的嵌套滚动 -->
                    <div style="max-height: 300px; overflow-y: auto; border: 2px dashed #f56c6c; padding: 8px;">
                        <div style="color: #f56c6c; font-size: 12px; margin-bottom: 8px;">内层滚动容器（问题源）</div>
                        
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">API Key</label>
                            <input type="text" class="auto-learning-input" placeholder="输入API Key">
                        </div>
                        
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-checkbox">
                                <input type="checkbox" checked>
                                <span class="auto-learning-checkbox-label">选项1</span>
                            </label>
                        </div>
                        
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-checkbox">
                                <input type="checkbox">
                                <span class="auto-learning-checkbox-label">选项2</span>
                            </label>
                        </div>
                        
                        <!-- 添加更多内容触发滚动 -->
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">测试字段1</label>
                            <input type="text" class="auto-learning-input">
                        </div>
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">测试字段2</label>
                            <input type="text" class="auto-learning-input">
                        </div>
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">测试字段3</label>
                            <input type="text" class="auto-learning-input">
                        </div>
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">测试字段4</label>
                            <input type="text" class="auto-learning-input">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 修复后的正确版本 -->
        <div class="demo-section">
            <div class="demo-title">修复后：单一滚动条</div>
            <div class="demo-description">只有外层容器有滚动，内层容器不设置overflow</div>
            
            <div class="status-indicator status-fixed">
                ✅ 修复：只使用外层滚动容器
            </div>
            
            <div class="auto-learning-panel">
                <div class="auto-learning-header">
                    <span>设置配置（修复版本）</span>
                </div>
                <div class="auto-learning-content">
                    <div class="auto-learning-tabs">
                        <div class="auto-learning-tab active">设置配置</div>
                    </div>
                    
                    <!-- 修复后：内层容器不设置overflow -->
                    <div class="auto-learning-settings-container">
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">百度OCR API Key</label>
                            <input type="text" class="auto-learning-input" placeholder="请输入百度API Key">
                        </div>

                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">百度OCR Secret Key</label>
                            <input type="password" class="auto-learning-input" placeholder="请输入百度Secret Key">
                        </div>

                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">操作延时时间（秒）</label>
                            <input type="number" class="auto-learning-input" value="1" min="0.5" max="10" step="0.5">
                        </div>

                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">自动选修课程数量</label>
                            <input type="number" class="auto-learning-input" value="10" min="1" max="50">
                        </div>

                        <div class="auto-learning-form-group">
                            <label class="auto-learning-checkbox">
                                <input type="checkbox" checked>
                                <span class="auto-learning-checkbox-label">🔇 自动静音视频</span>
                            </label>
                        </div>

                        <div class="auto-learning-form-group">
                            <label class="auto-learning-checkbox">
                                <input type="checkbox" checked>
                                <span class="auto-learning-checkbox-label">🔄 自动切换课件</span>
                            </label>
                        </div>

                        <div class="auto-learning-form-group">
                            <label class="auto-learning-checkbox">
                                <input type="checkbox">
                                <span class="auto-learning-checkbox-label">📖 自动切换课程</span>
                            </label>
                        </div>

                        <div class="auto-learning-form-group">
                            <label class="auto-learning-checkbox">
                                <input type="checkbox" checked>
                                <span class="auto-learning-checkbox-label">📚 自动添加选修课程</span>
                            </label>
                        </div>
                        
                        <!-- 添加更多内容来测试滚动 -->
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">额外测试字段1</label>
                            <input type="text" class="auto-learning-input" placeholder="测试输入1">
                        </div>
                        
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">额外测试字段2</label>
                            <input type="text" class="auto-learning-input" placeholder="测试输入2">
                        </div>

                        <div class="auto-learning-form-group">
                            <button class="auto-learning-button">保存设置</button>
                            <button class="auto-learning-button danger">重置设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 13px; color: #666; max-width: 1000px; margin-left: auto; margin-right: auto;">
        <h4 style="margin-top: 0; color: #333;">修复说明：</h4>
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>问题原因</strong>：外层 .auto-learning-content 和内层 .auto-learning-settings-container 都设置了 overflow-y: auto</li>
            <li><strong>修复方案</strong>：移除内层容器的 overflow-y 和 max-height 属性</li>
            <li><strong>效果</strong>：只有外层容器处理滚动，避免双滚动条问题</li>
            <li><strong>滚动条样式</strong>：统一使用主面板的滚动条样式</li>
        </ul>
    </div>
</body>
</html>
