<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局和滚动条测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
        }
        
        .auto-learning-panel {
            position: relative;
            width: 420px;
            max-height: 600px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            overflow: hidden;
        }
        
        .auto-learning-header {
            background: #409eff;
            color: white;
            padding: 12px 16px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .auto-learning-content {
            padding: 16px;
            max-height: 500px;
            overflow-y: auto;
            position: relative;
            padding-bottom: 80px; /* 为底部按钮留出空间 */
        }
        
        .auto-learning-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 16px;
        }
        
        .auto-learning-tab {
            padding: 8px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        
        .auto-learning-tab.active {
            color: #409eff;
            border-bottom-color: #409eff;
        }
        
        .auto-learning-form-group {
            margin-bottom: 16px;
        }
        
        .auto-learning-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .auto-learning-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .auto-learning-input:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            outline: none;
        }
        
        .auto-learning-input[id="account-input"] {
            min-height: 120px;
            line-height: 1.5;
            font-size: 13px;
            resize: vertical;
            font-family: monospace;
        }
        
        .auto-learning-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.3s ease;
        }
        
        .auto-learning-button:hover {
            background: #66b1ff;
        }
        
        .auto-learning-button.danger {
            background: #f56c6c;
        }
        
        .auto-learning-button.danger:hover {
            background: #f78989;
        }
        
        .auto-learning-checkbox {
            display: flex !important;
            align-items: center !important;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            user-select: none;
        }

        .auto-learning-checkbox:hover {
            background: #e9ecef;
            border-color: #409eff;
        }

        .auto-learning-checkbox input[type="checkbox"] {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            margin: 0 12px 0 0 !important;
            padding: 0 !important;
            width: 18px !important;
            height: 18px !important;
            min-width: 18px !important;
            min-height: 18px !important;
            max-width: 18px !important;
            max-height: 18px !important;
            cursor: pointer !important;
            accent-color: #409eff;
            -webkit-appearance: checkbox !important;
            -moz-appearance: checkbox !important;
            appearance: checkbox !important;
            position: relative !important;
            flex-shrink: 0 !important;
            border: 1px solid #ccc !important;
            border-radius: 2px !important;
            background: white !important;
            outline: none !important;
            box-shadow: none !important;
            transform: none !important;
            z-index: 1 !important;
        }

        .auto-learning-checkbox-label {
            font-weight: 500;
            color: #495057;
            flex: 1;
            cursor: pointer;
        }
        
        /* 账号操作按钮固定底部 */
        .auto-learning-accounts-actions {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            padding: 16px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: center;
            gap: 12px;
            border-radius: 0 0 12px 12px;
        }

        .auto-learning-accounts-actions .auto-learning-button {
            margin: 0;
        }
        
        /* 设置页面容器样式 */
        .auto-learning-settings-container {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 4px;
        }

        /* 滚动条美化 */
        .auto-learning-panel ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .auto-learning-panel ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb:active {
            background: #909090;
        }

        .auto-learning-panel ::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        /* 账号输入框滚动条 */
        .auto-learning-input[id="account-input"]::-webkit-scrollbar {
            width: 6px;
        }

        .auto-learning-input[id="account-input"]::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        .auto-learning-input[id="account-input"]::-webkit-scrollbar-thumb {
            background: #409eff;
            border-radius: 3px;
        }

        .auto-learning-input[id="account-input"]::-webkit-scrollbar-thumb:hover {
            background: #66b1ff;
        }

        /* 设置页面滚动条 */
        .auto-learning-settings-container::-webkit-scrollbar {
            width: 6px;
        }

        .auto-learning-settings-container::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        .auto-learning-settings-container::-webkit-scrollbar-thumb {
            background: #409eff;
            border-radius: 3px;
        }

        .auto-learning-settings-container::-webkit-scrollbar-thumb:hover {
            background: #66b1ff;
        }

        /* Firefox滚动条样式 */
        .auto-learning-panel {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }

        .auto-learning-input[id="account-input"] {
            scrollbar-width: thin;
            scrollbar-color: #409eff #f8f9fa;
        }

        .auto-learning-settings-container {
            scrollbar-width: thin;
            scrollbar-color: #409eff #f8f9fa;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 账号管理面板 -->
        <div class="auto-learning-panel">
            <div class="auto-learning-header">
                <span>账号管理</span>
            </div>
            <div class="auto-learning-content">
                <div class="auto-learning-tabs">
                    <div class="auto-learning-tab active">账号管理</div>
                    <div class="auto-learning-tab">设置配置</div>
                </div>
                
                <div class="auto-learning-form-group">
                    <label class="auto-learning-label">批量添加账号（一行一组：手机号码 密码）</label>
                    <textarea class="auto-learning-input" id="account-input" placeholder="每行一组账号密码，例如：&#10;*********** password123&#10;*********** password456" rows="6">*********** password123
*********** password456
*********** password789
*********** password000
*********** password111
*********** password222</textarea>
                    <div style="margin-top: 8px;">
                        <button class="auto-learning-button">批量添加</button>
                        <button class="auto-learning-button" style="background: #909399;">清空输入</button>
                    </div>
                </div>
                
                <div style="margin-bottom: 16px;">
                    <h4>已添加的账号：</h4>
                    <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span style="margin-right: 12px; font-size: 12px; padding: 4px 8px; border-radius: 12px; color: white; background: #67c23a;">已完成</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span style="margin-right: 12px; font-size: 12px; padding: 4px 8px; border-radius: 12px; color: white; background: #409eff;">学习中</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                </div>
                
                <!-- 固定在底部的按钮 -->
                <div class="auto-learning-accounts-actions">
                    <button class="auto-learning-button">开始学习</button>
                    <button class="auto-learning-button danger">停止学习</button>
                    <button class="auto-learning-button">清空账号</button>
                </div>
            </div>
        </div>
        
        <!-- 设置配置面板 -->
        <div class="auto-learning-panel">
            <div class="auto-learning-header">
                <span>设置配置</span>
            </div>
            <div class="auto-learning-content">
                <div class="auto-learning-tabs">
                    <div class="auto-learning-tab">账号管理</div>
                    <div class="auto-learning-tab active">设置配置</div>
                </div>
                
                <div class="auto-learning-settings-container">
                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">百度OCR API Key</label>
                        <input type="text" class="auto-learning-input" placeholder="请输入百度API Key">
                    </div>

                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">百度OCR Secret Key</label>
                        <input type="password" class="auto-learning-input" placeholder="请输入百度Secret Key">
                    </div>

                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">操作延时时间（秒）</label>
                        <input type="number" class="auto-learning-input" value="1" min="0.5" max="10" step="0.5">
                    </div>

                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">自动选修课程数量</label>
                        <input type="number" class="auto-learning-input" value="10" min="1" max="50">
                    </div>

                    <div class="auto-learning-form-group">
                        <label class="auto-learning-checkbox">
                            <input type="checkbox" checked>
                            <span class="auto-learning-checkbox-label">🔇 自动静音视频</span>
                        </label>
                    </div>

                    <div class="auto-learning-form-group">
                        <label class="auto-learning-checkbox">
                            <input type="checkbox" checked>
                            <span class="auto-learning-checkbox-label">🔄 自动切换课件</span>
                        </label>
                    </div>

                    <div class="auto-learning-form-group">
                        <label class="auto-learning-checkbox">
                            <input type="checkbox" checked>
                            <span class="auto-learning-checkbox-label">📖 自动切换课程</span>
                        </label>
                    </div>

                    <div class="auto-learning-form-group">
                        <label class="auto-learning-checkbox">
                            <input type="checkbox" checked>
                            <span class="auto-learning-checkbox-label">📚 自动添加选修课程</span>
                        </label>
                    </div>
                    
                    <!-- 添加更多内容来测试滚动 -->
                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">测试选项1</label>
                        <input type="text" class="auto-learning-input" placeholder="测试输入框1">
                    </div>
                    
                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">测试选项2</label>
                        <input type="text" class="auto-learning-input" placeholder="测试输入框2">
                    </div>
                    
                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">测试选项3</label>
                        <input type="text" class="auto-learning-input" placeholder="测试输入框3">
                    </div>

                    <div class="auto-learning-form-group">
                        <button class="auto-learning-button">保存设置</button>
                        <button class="auto-learning-button danger">重置设置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 30px; text-align: center; color: #666;">
        <h3>布局和滚动条测试</h3>
        <p><strong>左侧</strong>：账号管理面板，底部按钮固定居中</p>
        <p><strong>右侧</strong>：设置配置面板，滚动条美化</p>
    </div>
</body>
</html>
