<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>底部按钮固定修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
        }
        
        .auto-learning-panel {
            position: relative;
            width: 420px;
            max-height: 600px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            overflow: hidden;
        }
        
        .auto-learning-header {
            background: #409eff;
            color: white;
            padding: 12px 16px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .auto-learning-content {
            display: flex;
            flex-direction: column;
            max-height: 600px;
            min-height: 540px;
            overflow: hidden;
        }

        .auto-learning-content-scroll {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            padding-bottom: 16px;
        }
        
        .auto-learning-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 16px;
        }
        
        .auto-learning-tab {
            padding: 8px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        
        .auto-learning-tab.active {
            color: #409eff;
            border-bottom-color: #409eff;
        }
        
        .auto-learning-form-group {
            margin-bottom: 16px;
        }
        
        .auto-learning-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .auto-learning-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .auto-learning-input:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            outline: none;
        }
        
        .auto-learning-input[id="account-input"] {
            min-height: 120px;
            line-height: 1.5;
            font-size: 13px;
            resize: vertical;
            font-family: monospace;
        }
        
        .auto-learning-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.3s ease;
        }
        
        .auto-learning-button:hover {
            background: #66b1ff;
        }
        
        .auto-learning-button.danger {
            background: #f56c6c;
        }
        
        .auto-learning-button.danger:hover {
            background: #f78989;
        }
        
        .auto-learning-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .auto-learning-account-item {
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 8px;
        }
        
        .auto-learning-account-item:hover {
            background: #e9ecef;
            border-color: #409eff;
        }
        
        .auto-learning-account-phone {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }
        
        .auto-learning-account-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            white-space: nowrap;
            min-width: 60px;
            text-align: center;
        }
        
        .auto-learning-status-pending { background: #909399; }
        .auto-learning-status-running { background: #409eff; }
        .auto-learning-status-completed { background: #67c23a; }
        .auto-learning-status-error { background: #f56c6c; }
        
        /* 账号操作按钮固定底部 - 修复后的版本 */
        .auto-learning-accounts-actions {
            flex-shrink: 0;
            background: #fff;
            padding: 16px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: center;
            gap: 12px;
            border-radius: 0 0 12px 12px;
        }

        .auto-learning-accounts-actions .auto-learning-button {
            margin: 0;
        }
        
        /* 滚动条美化 */
        .auto-learning-panel ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .auto-learning-panel ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .auto-learning-panel ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .demo-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .status-indicator {
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .status-fixed {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-problem {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 修复前的问题演示 -->
        <div class="demo-section">
            <div class="demo-title">修复前：绝对定位问题</div>
            <div class="demo-description">使用position: absolute，当内容过多时按钮位置不正确</div>
            
            <div class="status-indicator status-problem">
                ❌ 问题：按钮位置随内容变化
            </div>
            
            <div class="auto-learning-panel">
                <div class="auto-learning-header">
                    <span>账号管理（问题版本）</span>
                </div>
                <div style="padding: 16px; max-height: 500px; overflow-y: auto; position: relative; padding-bottom: 80px;">
                    <div class="auto-learning-tabs">
                        <div class="auto-learning-tab active">账号管理</div>
                    </div>
                    
                    <div class="auto-learning-form-group">
                        <label class="auto-learning-label">批量添加账号</label>
                        <textarea class="auto-learning-input" id="account-input" rows="4">*********** password123
*********** password456</textarea>
                    </div>
                    
                    <!-- 模拟大量账号 -->
                    <div class="auto-learning-account-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span class="auto-learning-account-status auto-learning-status-completed" style="margin-right: 12px;">已完成</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <!-- 重复多个账号项来模拟长列表 -->
                    <div class="auto-learning-account-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span class="auto-learning-account-status auto-learning-status-running" style="margin-right: 12px;">学习中</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <div class="auto-learning-account-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span class="auto-learning-account-status auto-learning-status-pending" style="margin-right: 12px;">等待中</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <div class="auto-learning-account-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span class="auto-learning-account-status auto-learning-status-error" style="margin-right: 12px;">失败</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <div class="auto-learning-account-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span class="auto-learning-account-status auto-learning-status-completed" style="margin-right: 12px;">已完成</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <div class="auto-learning-account-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                            <span class="auto-learning-account-status auto-learning-status-running" style="margin-right: 12px;">学习中</span>
                            <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                        </div>
                    </div>
                    
                    <!-- 问题版本：绝对定位的按钮 -->
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: #fff; padding: 16px; border-top: 1px solid #eee; display: flex; justify-content: center; gap: 12px; border-radius: 0 0 12px 12px;">
                        <button class="auto-learning-button">开始学习</button>
                        <button class="auto-learning-button danger">停止学习</button>
                        <button class="auto-learning-button">清空账号</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 修复后的正确版本 -->
        <div class="demo-section">
            <div class="demo-title">修复后：Flexbox布局</div>
            <div class="demo-description">使用flex布局，按钮始终固定在底部</div>
            
            <div class="status-indicator status-fixed">
                ✅ 修复：按钮始终固定在底部
            </div>
            
            <div class="auto-learning-panel">
                <div class="auto-learning-header">
                    <span>账号管理（修复版本）</span>
                </div>
                <div class="auto-learning-content">
                    <div class="auto-learning-content-scroll">
                        <div class="auto-learning-tabs">
                            <div class="auto-learning-tab active">账号管理</div>
                        </div>
                        
                        <div class="auto-learning-form-group">
                            <label class="auto-learning-label">批量添加账号（一行一组：手机号码 密码）</label>
                            <textarea class="auto-learning-input" id="account-input" placeholder="每行一组账号密码，例如：&#10;*********** password123&#10;*********** password456" rows="6" style="resize: vertical; font-family: monospace;">*********** password123
*********** password456
*********** password789</textarea>
                            <div class="auto-learning-row" style="margin-top: 8px;">
                                <button class="auto-learning-button">批量添加</button>
                                <button class="auto-learning-button" style="background: #909399;">清空输入</button>
                            </div>
                        </div>
                        
                        <!-- 模拟大量账号 -->
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-completed" style="margin-right: 12px;">已完成</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                        
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-running" style="margin-right: 12px;">学习中</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                        
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-pending" style="margin-right: 12px;">等待中</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                        
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-error" style="margin-right: 12px;">失败</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                        
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-completed" style="margin-right: 12px;">已完成</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                        
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-running" style="margin-right: 12px;">学习中</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                        
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-pending" style="margin-right: 12px;">等待中</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                        
                        <div class="auto-learning-account-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1; margin-right: 12px; font-weight: 500;">***********</div>
                                <span class="auto-learning-account-status auto-learning-status-completed" style="margin-right: 12px;">已完成</span>
                                <button class="auto-learning-button danger" style="margin: 0; padding: 4px 8px; font-size: 12px;">删除</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 修复后：使用flex布局的按钮 -->
                    <div class="auto-learning-accounts-actions">
                        <button class="auto-learning-button">开始学习</button>
                        <button class="auto-learning-button danger">停止学习</button>
                        <button class="auto-learning-button">清空账号</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 13px; color: #666; max-width: 1200px; margin-left: auto; margin-right: auto;">
        <h4 style="margin-top: 0; color: #333;">修复说明：</h4>
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>问题原因</strong>：使用 position: absolute 定位底部按钮，当内容超出容器高度时位置不正确</li>
            <li><strong>修复方案</strong>：使用 Flexbox 布局，将内容区域设为 flex: 1，按钮区域设为 flex-shrink: 0</li>
            <li><strong>布局结构</strong>：外层容器 display: flex; flex-direction: column，内容区域可滚动，按钮区域固定</li>
            <li><strong>效果</strong>：无论账号列表有多长，底部按钮始终固定在面板底部</li>
        </ul>
    </div>
</body>
</html>
