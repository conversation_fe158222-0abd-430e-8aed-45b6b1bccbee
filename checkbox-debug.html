<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkbox调试测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .auto-learning-form-group {
            margin-bottom: 16px;
        }
        
        .auto-learning-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .auto-learning-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .auto-learning-checkbox {
            display: flex !important;
            align-items: center !important;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            user-select: none;
        }

        .auto-learning-checkbox:hover {
            background: #e9ecef;
            border-color: #409eff;
        }

        .auto-learning-checkbox input[type="checkbox"] {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            margin-right: 12px;
            width: 18px !important;
            height: 18px !important;
            cursor: pointer;
            accent-color: #409eff;
            -webkit-appearance: checkbox;
            -moz-appearance: checkbox;
            appearance: checkbox;
            position: relative;
            flex-shrink: 0;
        }

        .auto-learning-checkbox-label {
            font-weight: 500;
            color: #495057;
            flex: 1;
            cursor: pointer;
        }
        
        .auto-learning-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 12px;
        }
        
        .result {
            background: #f0f9ff;
            border: 1px solid #e0f2fe;
            border-radius: 4px;
            padding: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Checkbox调试测试</h1>
        
        <div class="debug-info">
            <strong>调试信息：</strong><br>
            - 使用了 !important 强制显示样式<br>
            - 添加了 -webkit-appearance 和 appearance 属性<br>
            - 设置了 flex-shrink: 0 防止压缩<br>
            - 添加了 user-select: none 防止文本选择
        </div>
        
        <div class="auto-learning-form-group">
            <label class="auto-learning-label">百度OCR API Key</label>
            <input type="text" class="auto-learning-input" id="baidu-ak" placeholder="请输入百度API Key">
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-label">百度OCR Secret Key</label>
            <input type="password" class="auto-learning-input" id="baidu-sk" placeholder="请输入百度Secret Key">
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-label">操作延时时间（秒）</label>
            <input type="number" class="auto-learning-input" id="delay-time" value="1" min="0.5" max="10" step="0.5">
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-label">自动选修课程数量</label>
            <input type="number" class="auto-learning-input" id="course-count" value="10" min="1" max="50">
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox">
                <input type="checkbox" id="auto-mute" checked>
                <span class="auto-learning-checkbox-label">🔇 自动静音视频</span>
            </label>
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox">
                <input type="checkbox" id="auto-switch-courseware" checked>
                <span class="auto-learning-checkbox-label">🔄 自动切换课件</span>
            </label>
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox">
                <input type="checkbox" id="auto-switch-course" checked>
                <span class="auto-learning-checkbox-label">📖 自动切换课程</span>
            </label>
        </div>

        <div class="auto-learning-form-group">
            <label class="auto-learning-checkbox">
                <input type="checkbox" id="auto-select-course" checked>
                <span class="auto-learning-checkbox-label">📚 自动添加选修课程</span>
            </label>
        </div>

        <div class="auto-learning-form-group">
            <button class="auto-learning-button" onclick="showSettings()">显示当前设置</button>
            <button class="auto-learning-button" onclick="debugCheckboxes()">调试Checkbox</button>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <h3>当前设置：</h3>
            <div id="result-content"></div>
        </div>
    </div>
    
    <script>
        function showSettings() {
            const settings = {
                baiduAK: document.getElementById('baidu-ak').value,
                baiduSK: document.getElementById('baidu-sk').value,
                delayTime: document.getElementById('delay-time').value,
                courseCount: document.getElementById('course-count').value,
                autoMute: document.getElementById('auto-mute').checked,
                autoSwitchCourseware: document.getElementById('auto-switch-courseware').checked,
                autoSwitchCourse: document.getElementById('auto-switch-course').checked,
                autoSelectCourse: document.getElementById('auto-select-course').checked
            };
            
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');
            
            let html = '<ul>';
            for (const [key, value] of Object.entries(settings)) {
                if (typeof value === 'boolean') {
                    html += `<li><strong>${key}:</strong> ${value ? '✅ 启用' : '❌ 禁用'}</li>`;
                } else {
                    html += `<li><strong>${key}:</strong> ${value}</li>`;
                }
            }
            html += '</ul>';
            
            resultContent.innerHTML = html;
            resultDiv.style.display = 'block';
        }
        
        function debugCheckboxes() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            let debugInfo = '<h4>Checkbox调试信息：</h4><ul>';
            
            checkboxes.forEach((checkbox, index) => {
                const computedStyle = window.getComputedStyle(checkbox);
                debugInfo += `<li>
                    <strong>Checkbox ${index + 1} (${checkbox.id}):</strong><br>
                    - Display: ${computedStyle.display}<br>
                    - Visibility: ${computedStyle.visibility}<br>
                    - Opacity: ${computedStyle.opacity}<br>
                    - Width: ${computedStyle.width}<br>
                    - Height: ${computedStyle.height}<br>
                    - Checked: ${checkbox.checked}<br>
                    - Parent display: ${window.getComputedStyle(checkbox.parentElement).display}
                </li>`;
            });
            
            debugInfo += '</ul>';
            
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('result-content');
            
            resultContent.innerHTML = debugInfo;
            resultDiv.style.display = 'block';
        }
        
        // 页面加载完成后自动调试
        window.addEventListener('load', () => {
            console.log('页面加载完成，开始调试checkbox');
            setTimeout(debugCheckboxes, 1000);
        });
    </script>
</body>
</html>
