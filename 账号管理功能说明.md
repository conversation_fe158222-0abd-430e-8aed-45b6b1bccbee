# 江西干部网络学院自动学习助手 - 账号管理功能更新

## 更新内容

### 🆕 多行账号输入
- **原功能**: 单行输入，一次只能添加一个账号
- **新功能**: 多行输入，支持批量添加多个账号
- **格式**: 每行一组账号密码，格式为 `手机号码 密码`

### 🔄 自动保存输入
- **功能**: 输入内容自动保存到本地存储
- **恢复**: 重新打开面板时自动恢复之前输入的内容
- **清空**: 提供"清空输入"按钮快速清除输入框内容

### 📊 批量处理结果
- **成功统计**: 显示成功添加的账号数量
- **错误提示**: 详细显示每行的错误信息
- **格式验证**: 自动验证手机号格式和重复检查

## 使用方法

### 1. 打开账号管理面板
- 点击右下角的浮动按钮
- 选择"账号管理"标签页

### 2. 批量添加账号
在多行输入框中输入账号信息，每行一组：
```
13800138000 password123
13900139000 password456
13700137000 password789
```

### 3. 点击"批量添加"按钮
- 系统会自动验证每行的格式
- 显示添加结果和错误信息
- 成功添加的账号会出现在账号列表中

### 4. 自动保存功能
- 输入内容会自动保存
- 下次打开面板时会自动恢复
- 可以使用"清空输入"按钮清除内容

## 输入格式要求

### ✅ 正确格式
```
13800138000 password123
13900139000 password456
13700137000 password789
```

### ❌ 错误格式
```
13800138000password123     # 缺少空格分隔
138001380001 password123   # 手机号格式错误
13800138000                # 缺少密码
```

## 验证规则

### 手机号验证
- 必须是11位数字
- 必须以1开头
- 第二位必须是3-9之间的数字

### 密码验证
- 不能为空
- 支持任意字符（建议使用字母数字组合）

### 重复检查
- 自动检查手机号是否已存在
- 重复的手机号会被跳过并提示错误

## 错误处理

### 常见错误类型
1. **格式错误**: 每行必须包含且仅包含手机号和密码两部分
2. **手机号格式错误**: 不符合中国大陆手机号格式
3. **手机号已存在**: 该手机号已经添加过

### 错误提示
- 显示具体的行号和错误原因
- 最多显示前5个错误，超过部分会显示数量统计
- 错误不会影响其他正确格式的账号添加

## 技术特性

### 🔒 数据安全
- 账号信息使用Tampermonkey的GM_setValue安全存储
- 输入内容临时保存在localStorage中
- 不会向外部服务器发送账号信息

### ⚡ 性能优化
- 异步处理大量账号添加
- 实时输入保存，避免数据丢失
- 智能错误处理，不会因单个错误影响整体处理

### 🎨 用户体验
- 美化的多行输入框
- 清晰的操作按钮布局
- 详细的处理结果反馈
- 中文界面和提示信息

## 注意事项

1. **输入内容会自动保存**: 请注意不要在输入框中留下敏感信息
2. **批量添加限制**: 建议单次添加不超过50个账号，避免界面卡顿
3. **格式严格**: 每行必须严格按照"手机号 密码"的格式，用空格分隔
4. **清空功能**: 使用"清空输入"按钮会立即清除所有输入内容和自动保存的数据

## 更新日志

### v2.0.1 (当前版本)
- ✅ 新增多行账号输入功能
- ✅ 新增自动保存输入内容功能
- ✅ 新增批量处理结果显示
- ✅ 优化账号验证逻辑
- ✅ 美化输入界面样式
- ✅ 完善错误处理机制
